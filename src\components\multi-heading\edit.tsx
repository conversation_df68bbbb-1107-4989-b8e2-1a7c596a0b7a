// External Dependencies.
import React, { ReactElement } from 'react';
import classnames from 'classnames';

// WordPress Dependencies.
declare const wp: any;

// Divi Dependencies.
import { ModuleContainer } from '@divi/module';

// Local Dependencies.

import { ModuleStyles } from './styles';
import { moduleClassnames } from './module-classnames';
import { ModuleScriptData } from './module-script-data';
import { MultiHeadingEditProps } from './types';

/**
 * Advanced Heading edit component of visual builder.
 *
 * @since ??
 *
 * @param {MultiHeadingEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const AdvancedHeadingEdit = (props: MultiHeadingEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
  } = props;

  // const toggle = attrs?.hideShow?.innerContent?.desktop?.value;

  // all vairables here
  const title1DisplayType = attrs?.title1?.advanced?.display?.desktop?.value ?? '';
  const title2DisplayType = attrs?.title2?.advanced?.display?.desktop?.value ?? '';
  const title3DisplayType = attrs?.title3DisplayType?.innerContent?.desktop?.value ?? '';

// title1
  let title1Type = attrs?.title1?.advanced?.style?.desktop?.value ?? "";
  let title1img = attrs?.title1?.decoration?.background?.desktop?.value?.image?.url ?? "";
  let title1Color = attrs?.title1?.decoration?.background?.desktop?.value?.gradient?.enabled ?? " ";
  const title1Speed = attrs?.title1?.advanced?.animation?.desktop?.value ?? " ";
  const title1StrokColor = attrs?.title1?.advanced?.strock?.desktop?.value ?? "null";
  const title1StrokBorder = attrs?.title1?.advanced?.borderWidth?.desktop?.value ?? "null";

  // title2
  let title2Type = attrs?.title2?.advanced?.style?.desktop?.value ?? "";
  let title2img = attrs?.title2?.decoration?.background?.desktop?.value?.image?.url ?? "";
  let title2Color = attrs?.title2?.decoration?.background?.desktop?.value?.gradient?.enabled ?? " ";
  const title2Speed = attrs?.title2?.advanced?.animation?.desktop?.value ?? " ";
  const title2StrokColor = attrs?.title2?.advanced?.strock?.desktop?.value ?? "null";
  const title2StrokBorder = attrs?.title2?.advanced?.borderWidth?.desktop?.value ?? "null";

  // Title3
  let title3Type = attrs?.title3?.advanced?.style?.desktop?.value ?? "";
  let title3img = attrs?.title3?.decoration?.background?.desktop?.value?.image?.url ?? "";
  let title3Color = attrs?.title3?.decoration?.background?.desktop?.value?.gradient?.enabled ?? " ";
  const title3Speed = attrs?.title3?.advanced?.animation?.desktop?.value ?? " ";
  const title3StrokColor = attrs?.title3?.advanced?.strock?.desktop?.value ?? "null";
  const title3StrokBorder = attrs?.title3?.advanced?.borderWidth?.desktop?.value ?? "null"; 


  //Titles inline dynamic Style
  const title1Style = {
    display: title1DisplayType,
    WebkitTextFillColor:   title1img !== "" || title1Color === 'on' || title1Color !== " " ? 'transparent' : undefined, // Safari
    textFillColor:   title1img !== "" || title1Color === 'on' ? 'transparent' : undefined, // Chrome
    color: title1StrokColor !== "null" ? 'transparent' : undefined,
    WebkitTextStroke: title1Type === "stock" || title1StrokColor !== "null" || title1StrokBorder !== "null" ? title1StrokBorder + title1StrokColor : undefined,
    animationDuration: title1Speed ,
  }

  const title2Style = {
    display: title2DisplayType,
    WebkitTextFillColor:   title2img !== "" || title2Color === 'on' || title2Color !== " " ? 'transparent' : undefined, // Safari
    textFillColor:   title2img !== "" || title2Color === 'on' ? 'transparent' : undefined, // Chrome
    color: title2StrokColor !== "null" ? 'transparent' : undefined,
    WebkitTextStroke: title2Type === "stock" || title2StrokColor !== "null" || title2StrokBorder !== "null" ? title2StrokBorder + title2StrokColor : undefined,
    animationDuration: title2Speed ,
  }

  const title3Style = {
    display: title3DisplayType,
    WebkitTextFillColor:   title3img !== "" || title3Color === 'on' || title3Color !== " " ? 'transparent' : undefined, // Safari
    textFillColor:   title3img !== "" || title3Color === 'on' ? 'transparent' : undefined, // Chrome
    color: title3StrokColor !== "null" ? 'transparent' : undefined,
    WebkitTextStroke: title3Type === "stock" || title3StrokColor !== "null" || title3StrokBorder !== "null" ? title3StrokBorder + title3StrokColor : undefined,
    animationDuration: title3Speed ,
  };

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      scriptDataComponent={ModuleScriptData}
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <div className="advanced_heading__container" >
        {elements.render({
          attrName: 'title1',
          htmlAttributes: {
            style: title1Style
          }
        })}
        {elements.render({
          attrName: 'title2',
          htmlAttributes: {
            style: title2Style
          }
        })}
        {elements.render({
          attrName: 'title3',
          htmlAttributes: {
            style: title3Style
          }
        })}

      </div>
    </ModuleContainer>
  );
};
