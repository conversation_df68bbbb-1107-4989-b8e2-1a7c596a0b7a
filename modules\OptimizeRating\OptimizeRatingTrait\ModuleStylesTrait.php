<?php

/**
 * OptimizeRating::module_styles().
 *
 * @package MEE\Modules\OptimizeRating
 * @since ??
 */

namespace MEE\Modules\OptimizeRating\OptimizeRatingTrait;

if (! defined('ABSPATH')) {
	die('Direct access forbidden.');
}

use ET\Builder\FrontEnd\Module\Style;
use ET\Builder\Packages\Module\Options\Text\TextStyle;
use ET\Builder\Packages\Module\Options\Css\CssStyle;
use MEE\Modules\OptimizeRating\OptimizeRating;

trait ModuleStylesTrait
{

	use CustomCssTrait;

	/**
	 * Static Module's style components.
	 *
	 * This function is equivalent of JS function ModuleStyles located in
	 * src/components/OptimizeRating/styles.tsx.
	 *
	 * @since ??
	 *
	 * @param array $args {
	 *     An array of arguments.
	 *
	 *      @type string $id                Module ID. In VB, the ID of module is UUIDV4. In FE, the ID is order index.
	 *      @type string $name              Module name.
	 *      @type string $attrs             Module attributes.
	 *      @type string $parentAttrs       Parent attrs.
	 *      @type string $orderClass        Selector class name.
	 *      @type string $parentOrderClass  Parent selector class name.
	 *      @type string $wrapperOrderClass Wrapper selector class name.
	 *      @type string $settings          Custom settings.
	 *      @type string $state             Attributes state.
	 *      @type string $mode              Style mode.
	 *      @type ModuleElements $elements  ModuleElements instance.
	 * }
	 */
	public static function module_styles($args)
	{
		$attrs    = $args['attrs'] ?? [];
		$elements = $args['elements'];
		$settings = $args['settings'] ?? [];
		$orderClass = $args['orderClass'];


		$filledStarsSelector = "{$orderClass} .dotm_optimize_rating_star.dotm_optimize_rating_star_filled";
		$starsSelector = "{$orderClass} .dotm_optimize_rating_stars";
		$tooltipSelector = "{$orderClass} .dotm_optimize_rating_tooltip";
		$containerSelector = "{$orderClass} .dotm_optimize_rating_container";
		$hoverTooltipSelector = "{$orderClass} .dotm_optimize_rating_stars:hover .dotm_optimize_rating_tooltip";

		$icon_type = $attrs['rating']['advanced']['icon_type']['desktop']['value'] ?? '';
		$alignment = $attrs['rating']['advanced']['alignment']['desktop']['value'] ?? '';
		$hr_position = $attrs['tooltip']['decoration']['hr_position']['desktop']['value'] ?? '';
		$use_tooltip = $attrs['tooltip']['decoration']['use_tooltip']['desktop']['value'] ?? '';

		Style::add(
			[
				'id'            => $args['id'],
				'name'          => $args['name'],
				'orderIndex'    => $args['orderIndex'],
				'storeInstance' => $args['storeInstance'],
				'styles'        => [
					// Module.
					$elements->style(
						[
							'attrName'   => 'module',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $filledStarsSelector,
											'attr'     => $attrs['rating']['advanced']['star_active_color'] ?? [],
											'property' => 'color'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_color'] ?? [],
											'property' => 'color'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_size'] ?? [],
											'property' => 'font-size'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['star_gap'] ?? [],
											'property' => 'letter-spacing'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $starsSelector,
											'attr'     => $attrs['rating']['advanced']['icon_type'] ?? [],
											'property' => $icon_type === "normal" ? "font-weight: 900;" : ''
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $filledStarsSelector,
											'attr'     => $attrs['rating']['advanced']['icon_type'] ?? [],
											'property' => $icon_type === "filled" ? "font-weight: 900;" : ''
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $containerSelector,
											'attr'     => $attrs['rating']['advanced']['alignment'] ?? [],
											'property' => 'justify-content'
										]
									],
								]
							],
						]
					),

					// tooltip.
					$elements->style(
						[
							'attrName' => 'tooltip',
							'styleProps' => [
								'disabledOn'     => [
									'disabledModuleVisibility' => $settings['disabledModuleVisibility'] ?? null,
								],
								'advancedStyles' => [
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipSelector,
											'attr'     => $attrs['tooltip']['decoration']['tooltip_BGcolor'] ?? [],
											'property' => 'background-color'
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $tooltipSelector,
											'attr'     => $attrs['tooltip']['decoration']['hr_position'] ?? [],
											'property' => "left: {$hr_position};"
										]
									],
									[
										'componentName' => 'divi/common',
										'props'         => [
											'selector' => $hoverTooltipSelector,
											'attr'     => $attrs['tooltip']['decoration']['use_tooltip'] ?? [],
											'property' => $use_tooltip === "on" ? "opacity: 1; visibility: visible;" : ''
										]
									],
								]
							],

						]
					),

					// Title.
					$elements->style(
						[
							'attrName' => 'title',
						]
					),

					/*
					 * We need to add CssStyle at the very bottom of other
					 * components so that custom css can override module styles
					 * till we find a more elegant solution.
					 */
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeRating::custom_css(),
						]
					),

					// ATTENTION: The code is intentionally added and commented in FE only as an example of expected value format.
					// If you have custom style processing, the style output should be passed as an `array` of style declarations
					// to the `styles` property of the `Style::add` method. For example:
					// [
					// 	[
					// 		'atRules'     => false,
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: red;'
					// 	],
					// 	[
					// 		'atRules'     => '@media only screen and (max-width: 767px)',
					// 		'selector'    => "{$args['orderClass']} .example_static_module__content-container",
					// 		'declaration' => 'color: green;'
					// 	],
					// ],

					// The code below is an example of how to use the `CssStyle::style` method to generate CSS style.
					CssStyle::style(
						[
							'selector'  => $args['orderClass'],
							'attr'      => $attrs['css'] ?? [],
							'cssFields' => OptimizeRating::custom_css(),
						]
					),
				],
			]
		);
	}
}
