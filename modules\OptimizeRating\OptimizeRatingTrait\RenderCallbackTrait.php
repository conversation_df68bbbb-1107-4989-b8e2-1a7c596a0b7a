<?php
/**
 * OptimizeRating::render_callback()
 *
 * @package MEE\Modules\OptimizeRating
 * @since ??
 */

namespace MEE\Modules\OptimizeRating\OptimizeRatingTrait;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

// phpcs:disable ET.Sniffs.ValidVariableName.UsedPropertyNotSnakeCase -- WP use snakeCase in \WP_Block_Parser_Block

use ET\Builder\Packages\Module\Module;
use ET\Builder\Framework\Utility\HTMLUtility;
use ET\Builder\FrontEnd\BlockParser\BlockParserStore;
use ET\Builder\Packages\Module\Options\Element\ElementComponents;
use MEE\Modules\OptimizeRating\OptimizeRating;

trait RenderCallbackTrait {

	/**
	 * OptimizeRating render callback which outputs server side rendered HTML on the Front-End.
	 *
	 * @since ??
	 * @param array          $attrs    Block attributes that were saved by VB.
	 * @param string         $content  Block content.
	 * @param WP_Block       $block    Parsed block object that being rendered.
	 * @param ModuleElements $elements ModuleElements instance.
	 *
	 * @return string HTML rendered of OptimizeRating.
	 */
	public static function render_callback( $attrs, $content, $block, $elements ) {

		$maxRating = $attrs['rating']['advanced']['rating_range']['desktop']['value'] ?? '';
		$currentRating = (int) ($attrs['rating']['advanced']['rating']['desktop']['value'] ?? '');

		$uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyz', ceil(10/strlen($x)) )), 1, 10) . (new \DateTime())->format('U');


		$option = [
			'maxRating' => $maxRating,
			'currentRating' => $currentRating,
			'containerId' => $uuid,
		];
		

		// Title.
		$title = $elements->render(
			[
				'attrName' => 'title',
			]
		);

		// stars.
		$stars_container = HTMLUtility::render(
			[
				'tag'               => 'div',
				'attributes'        => [
					'class' => 'dotm_optimize_rating_stars',
				],
				'childrenSanitizer' => 'et_core_esc_previously',
				'children'          => '',
			]
		);

		$parent       = BlockParserStore::get_parent( $block->parsed_block['id'], $block->parsed_block['storeInstance'] );
		$parent_attrs = $parent->attrs ?? [];

		return Module::render(
			[
				// FE only.
				'orderIndex'          => $block->parsed_block['orderIndex'],
				'storeInstance'       => $block->parsed_block['storeInstance'],

				// VB equivalent.
				'attrs'               => $attrs,
				'elements'            => $elements,
				'id'                  => $block->parsed_block['id'],
				'name'                => $block->block_type->name,
				'moduleCategory'      => $block->block_type->category,
				'classnamesFunction'  => [ OptimizeRating::class, 'module_classnames' ],
				'stylesComponent'     => [ OptimizeRating::class, 'module_styles' ],
				'scriptDataComponent' => [ OptimizeRating::class, 'module_script_data' ],
				'parentAttrs'         => $parent_attrs,
				'parentId'            => $parent->id ?? '',
				'parentName'          => $parent->blockName ?? '',
				'children'            => [
					ElementComponents::component(
						[
							'attrs'         => $attrs['module']['decoration'] ?? [],
							'id'            => $block->parsed_block['id'],

							// FE only.
							'orderIndex'    => $block->parsed_block['orderIndex'],
							'storeInstance' => $block->parsed_block['storeInstance'],
						]
					),
					HTMLUtility::render(
						[
							'tag'               => 'div',
							'attributes'        => [
								'class' => 'dotm_optimize_rating_container',
								'id' => $uuid,
							],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children'          => $title . $stars_container,
						]
					),
					HTMLUtility::render(
						[
							'tag' => 'script',
							'attributes' => [],
							'childrenSanitizer' => 'et_core_esc_previously',
							'children' => "document.addEventListener('DOMContentLoaded', function() {
                                const options = " . json_encode($option) . ";
                                rating(options);
                            });",
						]
					),
				],
			]
		);
	}
}
