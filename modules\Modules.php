<?php
/**
 * Register all modules with dependency tree.
 *
 * @package MEE\Modules
 * @since ??
 */
namespace MEE\Modules;


use MEE\Modules\MaskText\MaskText;
use MEE\Modules\YoutubeVideo\YoutubeVideo;
use MEE\Modules\FbPage\FbPage;
use MEE\Modules\FbVideo\FbVideo;

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}


use MEE\Modules\MultiHeading\MultiHeading;
use MEE\Modules\PriceList\PriceList;
use MEE\Modules\TypingEffect\TypingEffect;
use MEE\Modules\ImageAccordion\ImageAccordion;
use MEE\Modules\ImageAccordionItem\ImageAccordionItem;
use MEE\Modules\TextGradient\TextGradient;
use MEE\Modules\PriceListItem\PriceListItem;
use MEE\Modules\TextScroll\TextScroll;
use MEE\Modules\StepFlow\StepFlow;
use MEE\Modules\ProgressBar\ProgressBar;
use MEE\Modules\ProfileOptimaizer\ProfileOptimaizer;
use MEE\Modules\ProfileOptimazerItem\ProfileOptimazerItem;
use MEE\Modules\ImageHover\ImageHover;
use MEE\Modules\OptimizeFlipBox\OptimizeFlipBox;
use MEE\Modules\BeforeAfterSlider\BeforeAfterSlider;
use MEE\Modules\FacebookPost\FacebookPost;
use MEE\Modules\PdfViewer\PdfViewer;
use MEE\Modules\OptimizeCard\OptimizeCard;
use MEE\Modules\Breadcrumbs\Breadcrumbs;
use MEE\Modules\BreadcrumbsItem\BreadcrumbsItem;
use MEE\Modules\GoogleMap\GoogleMap;
use MEE\Modules\BusinessHour\BusinessHour;
use MEE\Modules\BusinessHourItem\BusinessHourItem;
use MEE\Modules\GlitchText\GlitchText;
use MEE\Modules\Masonary\Masonary;
use MEE\Modules\ImageLogoCarousel\ImageLogoCarousel;
use MEE\Modules\OptimizeCardCarousel\OptimizeCardCarousel;
use MEE\Modules\OptimizeCardCarouselItem\OptimizeCardCarouselItem;
use MEE\Modules\OptimizerShapes\OptimizerShapes;
use MEE\Modules\TextDivider\TextDivider;
use MEE\Modules\TextBadge\TextBadge;
use MEE\Modules\TextNotation\TextNotation;
use MEE\Modules\SocialShareButtons\SocialShareButtons;
use MEE\Modules\SocialShareButtonItem\SocialShareButtonItem;
use MEE\Modules\OptimizeImageReveal\OptimizeImageReveal;
use MEE\Modules\MotionTextColor\MotionTextColor;
use MEE\Modules\OptimizeRating\OptimizeRating;
use MEE\Modules\ShuffleLetter\ShuffleLetter;
use MEE\Modules\BlockRevealText\BlockRevealText;
use MEE\Modules\FacebookLikeButton\FacebookLikeButton;
use MEE\Modules\OptimizeIconList\OptimizeIconList;
use MEE\Modules\OptimizeIconItem\OptimizeIconItem;
use MEE\Modules\ImageMagnifier\ImageMagnifier;
use MEE\Modules\OptimizeTimeline\OptimizeTimeline;
use MEE\Modules\OptimizeTimelineItem\OptimizeTimelineItem;
use MEE\Modules\TwitterButton\TwitterButton;
use MEE\Modules\TwitterFollowButton\TwitterFollowButton;
use MEE\Modules\TwitterTimeline\TwitterTimeline;
use MEE\Modules\OptimizerCounter\OptimizerCounter;
use MEE\Modules\OptimizeCountDown\OptimizeCountDown;
use MEE\Modules\OptimizeTextContent\OptimizeTextContent;
use MEE\Modules\OptimizeFAQ\OptimizeFAQ;
use MEE\Modules\OptimizeFAQItem\OptimizeFAQItem;
use MEE\Modules\OptimizeButtons\OptimizeButtons;
use MEE\Modules\OptimizeButtonItem\OptimizeButtonItem;
use MEE\Modules\Testimonials\Testimonials;

add_action(
	'divi_module_library_modules_dependency_tree',
	function ( $dependency_tree ) {
		$dependency_tree->add_dependency( new PriceList() );
		$dependency_tree->add_dependency( new TypingEffect() );
		$dependency_tree->add_dependency( new ImageAccordion() );
		$dependency_tree->add_dependency( new ImageAccordionItem() );
		$dependency_tree->add_dependency( new TextGradient() );
		$dependency_tree->add_dependency( new PriceListItem() );
		$dependency_tree->add_dependency( new MultiHeading() );
		$dependency_tree->add_dependency( new TextScroll() );
		$dependency_tree->add_dependency( new StepFlow() );
		$dependency_tree->add_dependency( new ProgressBar() );
		$dependency_tree->add_dependency( new ProfileOptimaizer() );
		$dependency_tree->add_dependency( new ProfileOptimazerItem() );
		$dependency_tree->add_dependency( new ImageHover() );
		$dependency_tree->add_dependency( new OptimizeFlipBox() );
		$dependency_tree->add_dependency( new BeforeAfterSlider() );
		$dependency_tree->add_dependency( new FacebookPost() );
		$dependency_tree->add_dependency( new OptimizeCard() );
		$dependency_tree->add_dependency( new Breadcrumbs() );
		$dependency_tree->add_dependency( new BreadcrumbsItem() );
		$dependency_tree->add_dependency( new FbPage() );
		$dependency_tree->add_dependency( new GoogleMap() );
		$dependency_tree->add_dependency( new BusinessHourItem() );
		$dependency_tree->add_dependency( new BusinessHour() );
		$dependency_tree->add_dependency( new Masonary() );
		$dependency_tree->add_dependency( new ImageLogoCarousel() );
		$dependency_tree->add_dependency( new OptimizeCardCarousel() );
		$dependency_tree->add_dependency( new OptimizeCardCarouselItem() );
		$dependency_tree->add_dependency( new OptimizerShapes() );
		$dependency_tree->add_dependency( new TextDivider() );
		$dependency_tree->add_dependency( new TextBadge() );
		$dependency_tree->add_dependency( new TextNotation() );
		$dependency_tree->add_dependency( new GlitchText() );
		$dependency_tree->add_dependency( new SocialShareButtons() );
		$dependency_tree->add_dependency( new SocialShareButtonItem() );
		$dependency_tree->add_dependency( new OptimizeImageReveal() );
		$dependency_tree->add_dependency( new MotionTextColor() );
		$dependency_tree->add_dependency( new OptimizeRating() );
		$dependency_tree->add_dependency( new BlockRevealText() );
		$dependency_tree->add_dependency( new OptimizeIconList() );
		$dependency_tree->add_dependency( new OptimizeIconItem() );
		$dependency_tree->add_dependency( new MaskText() );
		$dependency_tree->add_dependency( new ShuffleLetter() );
		$dependency_tree->add_dependency( new ImageMagnifier() );
		$dependency_tree->add_dependency( new OptimizeTimeline() );
		$dependency_tree->add_dependency( new OptimizeTimelineItem() );
		$dependency_tree->add_dependency( new TwitterTimeline() );
		$dependency_tree->add_dependency( new FacebookLikeButton() );
		$dependency_tree->add_dependency( new TwitterButton() );
		$dependency_tree->add_dependency( new TwitterFollowButton() );
		$dependency_tree->add_dependency( new OptimizerCounter() );
		$dependency_tree->add_dependency( new OptimizeCountDown() );
		$dependency_tree->add_dependency( new OptimizeTextContent() );

		$dependency_tree->add_dependency( new OptimizeFAQ() );
		$dependency_tree->add_dependency( new OptimizeFAQItem() );
		$dependency_tree->add_dependency( new OptimizeButtons() );
		$dependency_tree->add_dependency( new OptimizeButtonItem() );
		$dependency_tree->add_dependency( new Testimonials() );
	}
); 